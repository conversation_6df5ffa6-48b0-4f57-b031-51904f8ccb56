# Selective Database Scaling

This document explains the selective database scaling feature that allows the db-restore-sanitize job to only scale up the database types that are actually needed based on the configuration.

## Overview

Previously, the db-restore-sanitize job would always scale up both MySQL and PostgreSQL deployments, regardless of which databases were actually configured for sanitization. This could lead to unnecessary resource usage and potential conflicts when multiple sanitization jobs were running.

The new selective scaling feature:

1. **Analyzes the configuration** to determine which database types are needed
2. **Only scales up required databases** (MySQL, PostgreSQL, or both)
3. **Prevents conflicts** between multiple sanitization jobs using a locking mechanism
4. **Supports multiple cronjobs** running different configurations without interference

## How It Works

### Configuration Analysis

The system analyzes the database configurations in the AWS Secrets Manager secret to determine which database types are enabled:

```json
{
  "databases": [
    {
      "type": "postgresql",
      "enabled": true,
      ...
    },
    {
      "type": "mysql", 
      "enabled": false,
      ...
    }
  ]
}
```

In this example, only PostgreSQL would be scaled up.

### Locking Mechanism

To prevent conflicts between multiple sanitization jobs:

1. **Lock Acquisition**: Before scaling up databases, the job creates a Kubernetes ConfigMap as a lock
2. **Conflict Prevention**: If another job already holds the lock, the new job will fail with an error
3. **Lock Release**: The lock is automatically released when databases are scaled down

### Multiple Cronjobs

You can now run multiple sanitization cronjobs with different schedules and configurations:

- **Primary Job**: `mneme-db-restore-sanitize` (runs at 8:00 AM MT)
- **Secondary Job**: `mneme-db-restore-sanitize-secondary` (runs at 2:00 PM MT)

Each job uses a different AWS Secrets Manager secret:
- Primary: `restore-sanitize-secret-arn`
- Secondary: `restore-sanitize-secondary-secret-arn`

## Configuration Examples

### PostgreSQL Only
```json
{
  "databases": [
    {
      "type": "postgresql",
      "host": "postgres",
      "port": 5432,
      "database": "ares_sanitize",
      "enabled": true,
      ...
    }
  ]
}
```
**Result**: Only PostgreSQL deployment is scaled up.

### MySQL Only
```json
{
  "databases": [
    {
      "type": "mysql",
      "host": "mysql", 
      "port": 3306,
      "database": "tracker_sanitize",
      "enabled": true,
      ...
    }
  ]
}
```
**Result**: Only MySQL deployment is scaled up.

### Mixed Configuration
```json
{
  "databases": [
    {
      "type": "postgresql",
      "enabled": true,
      ...
    },
    {
      "type": "mysql",
      "enabled": false,
      ...
    }
  ]
}
```
**Result**: Only PostgreSQL deployment is scaled up (MySQL is disabled).

## Benefits

1. **Resource Efficiency**: Only necessary databases are scaled up
2. **Conflict Prevention**: Multiple jobs can run without interfering with each other
3. **Flexibility**: Different schedules can run different database configurations
4. **Backward Compatibility**: Existing configurations continue to work unchanged

## Deployment

The new functionality includes:

1. **Updated DatabaseService**: Supports selective scaling and locking
2. **New Cronjob**: `db-restore-sanitize-secondary.yaml` for the second schedule
3. **Updated Secrets**: Added `restore-sanitize-secondary-secret-arn`
4. **Lock Management**: Automatic ConfigMap-based locking

## Monitoring

The logs will show which database types are being scaled:

```
Analyzed database configurations, required types: [mysql]
Scaling up database deployments for types: [mysql]
Successfully acquired database scaling lock
```

And during shutdown:

```
Scaling down database deployments for types: [mysql]
Successfully released database scaling lock
```
